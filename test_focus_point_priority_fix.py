#!/usr/bin/env python3
"""
测试关注点优先级配置修复
验证p0/p1/p2配置在关注点采集和文档生成中的一致性
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.config.unified_config_loader import get_unified_config
from backend.agents.conversation_flow.state_manager import StateManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.data.db.database_manager import DatabaseManager
from backend.agents.document_generator import DocumentGenerator


async def test_priority_configuration_consistency():
    """测试优先级配置的一致性"""
    print("🔍 测试关注点优先级配置一致性...")
    
    # 获取配置
    config = get_unified_config()
    p0_required = config.get_business_rule("business_rules.focus_point_priority.p0", True)
    p1_required = config.get_business_rule("business_rules.focus_point_priority.p1", True)
    p2_required = config.get_business_rule("business_rules.focus_point_priority.p2", False)
    
    print(f"当前配置: P0={p0_required}, P1={p1_required}, P2={p2_required}")
    
    # 模拟关注点数据
    test_focus_points = [
        {"id": "test_p0", "name": "P0关注点", "priority": "P0"},
        {"id": "test_p1", "name": "P1关注点", "priority": "P1"},
        {"id": "test_p2", "name": "P2关注点", "priority": "P2"},
    ]
    
    # 测试StateManager的check_all_required_covered方法
    print("\n📋 测试StateManager.check_all_required_covered方法...")
    
    # 创建测试实例
    db_manager = DatabaseManager()
    focus_point_manager = FocusPointManager(db_manager)
    state_manager = StateManager(db_manager, focus_point_manager)
    
    # 模拟会话数据
    test_session_id = "test_session_123"
    test_user_id = "test_user_456"
    
    try:
        # 初始化关注点
        await focus_point_manager.initialize_focus_points(test_session_id, test_user_id, test_focus_points)
        
        # 测试1: 没有完成任何关注点的情况
        print("测试1: 没有完成任何关注点")
        result = await state_manager.check_all_required_covered(test_session_id, test_user_id, test_focus_points)
        print(f"结果: {result} (应该为False)")
        
        # 测试2: 只完成P0关注点
        print("\n测试2: 只完成P0关注点")
        await focus_point_manager.update_focus_point_status(test_session_id, test_user_id, "test_p0", "completed", "P0完成")
        result = await state_manager.check_all_required_covered(test_session_id, test_user_id, test_focus_points)
        expected = not p1_required and not p2_required  # 如果只需要P0，应该为True
        print(f"结果: {result} (期望: {expected})")
        
        # 测试3: 完成P0和P1关注点
        print("\n测试3: 完成P0和P1关注点")
        await focus_point_manager.update_focus_point_status(test_session_id, test_user_id, "test_p1", "completed", "P1完成")
        result = await state_manager.check_all_required_covered(test_session_id, test_user_id, test_focus_points)
        expected = not p2_required  # 如果不需要P2，应该为True
        print(f"结果: {result} (期望: {expected})")
        
        # 测试4: 完成所有关注点
        print("\n测试4: 完成所有关注点")
        await focus_point_manager.update_focus_point_status(test_session_id, test_user_id, "test_p2", "completed", "P2完成")
        result = await state_manager.check_all_required_covered(test_session_id, test_user_id, test_focus_points)
        print(f"结果: {result} (应该为True)")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试get_next_pending_point方法
    print("\n📋 测试StateManager.get_next_pending_point方法...")
    
    try:
        # 重置关注点状态
        await focus_point_manager.reset_focus_points_status(test_session_id, test_user_id)
        
        # 测试获取下一个待处理关注点
        next_point = await state_manager.get_next_pending_point(test_session_id, test_user_id, test_focus_points)
        if next_point:
            print(f"下一个待处理关注点: {next_point['id']} ({next_point['name']}) - 优先级: {next_point['priority']}")
            
            # 验证是否符合配置要求
            priority = next_point.get("priority", "P0").upper()
            should_be_selected = False
            if priority == "P0" and p0_required:
                should_be_selected = True
            elif priority == "P1" and p1_required:
                should_be_selected = True
            elif priority == "P2" and p2_required:
                should_be_selected = True
            
            print(f"是否符合配置要求: {should_be_selected}")
        else:
            print("没有找到待处理的关注点")
            
    except Exception as e:
        print(f"❌ 测试get_next_pending_point时出现错误: {e}")
        import traceback
        traceback.print_exc()


def test_configuration_scenarios():
    """测试不同配置场景"""
    print("\n🔧 测试不同配置场景...")
    
    config = get_unified_config()
    
    scenarios = [
        {"name": "默认配置", "p0": True, "p1": True, "p2": False},
        {"name": "全部启用", "p0": True, "p1": True, "p2": True},
        {"name": "只启用P0", "p0": True, "p1": False, "p2": False},
        {"name": "启用P0和P2", "p0": True, "p1": False, "p2": True},
    ]
    
    for scenario in scenarios:
        print(f"\n场景: {scenario['name']}")
        print(f"配置: P0={scenario['p0']}, P1={scenario['p1']}, P2={scenario['p2']}")
        
        # 模拟配置
        test_focus_points = [
            {"id": "test_p0", "name": "P0关注点", "priority": "P0"},
            {"id": "test_p1", "name": "P1关注点", "priority": "P1"},
            {"id": "test_p2", "name": "P2关注点", "priority": "P2"},
        ]
        
        # 计算期望的必需关注点数量
        required_count = 0
        if scenario['p0']:
            required_count += 1
        if scenario['p1']:
            required_count += 1
        if scenario['p2']:
            required_count += 1
            
        print(f"期望需要采集的关注点数量: {required_count}")


async def main():
    """主测试函数"""
    print("🚀 开始测试关注点优先级配置修复...")
    
    try:
        await test_priority_configuration_consistency()
        test_configuration_scenarios()
        
        print("\n✅ 测试完成！")
        print("\n📝 修复总结:")
        print("1. StateManager.check_all_required_covered 现在根据配置检查所有必需的关注点")
        print("2. StateManager.get_next_pending_point 现在根据配置选择需要处理的关注点")
        print("3. FocusPointManager.get_next_pending_focus_point 也进行了相应修复")
        print("4. 确保了关注点采集和文档生成的配置一致性")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
